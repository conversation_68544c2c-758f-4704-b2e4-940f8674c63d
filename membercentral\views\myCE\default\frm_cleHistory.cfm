<cfset local.cleData = attributes.data>

<cfsavecontent variable="local.myCEFrmJS">
	<cfoutput>
		<style type="text/css">
			##periodStartDate, ##periodEndDate { 
				margin:0px!important;
				background-image:url("/assets/common/images/calendar/monthView.gif"); 
				background-position:right center; background-repeat:no-repeat; 
			}
			.Heading{font-size: 16px; font-family: 'Open Sans', Helvetica, sans-serif;color: ##3383af; letter-spacing: 0px; line-height: 18px;}
			.BodyTextTitle{text-transform:uppercase;font-size:16px;}
			.alertMsg {
				text-align: left;
				padding: 5px 20px 5px 45px;
				border: 2px solid ##fc6;
			}
			.printLogo{display:none !important}
			.b { font-weight: bold; }
			.cleTable-heading { border-bottom: 1px solid ##666; }
			.cleHistorySummary {
				color: ##666;
			}
			.HeaderText{padding-top:10px;padding-bottom:10px;}
			.span3.visible-phone { padding-left: 0px; }
			.cleRow.inDesktop { display:block; }
			.cleRow.inPhone { display:none; }				
			
			@media screen and (min-width: 320px) and (max-width: 767px){
				.DatePosition {width: 100%;  float: left;  clear: both;}
				.DateFieldPosition{float:left;clear: both;}
				.periodDate{float:left;clear:both;}
				.clearPosition{float: left; margin-left: 10px;}
				.searchPosition{vertical-align: bottom; width: 100%;float: left;}
				.cleHistoryWrap table.cleTable tr.mobileHide{display:none;}
				.cleHistoryWrap table.cleTable tr td{
					display: block;
					clear: both;
					text-align: left;
					position: relative;
					padding-top: 5px;
					font-family:'Open Sans', Helvetica, sans-serif!important;
				}					
				.cleHistoryWrap table.cleTable tr td:before{
					content: attr(data-title);					
					top:0;
					left:0;
					width: 100%;
					font-weight: bold;
				}	
				.hidein{
					display:none !important;
				}
				.nowraptext {
					width: unset!important;
					white-space: unset!important;
				}
			}
			.cleHistoryWrap table.cleTable th{border-bottom:1px solid ##666; }
			.cleTable {width:100%;}
			.cleTable th, .cleTable td{
				border-top:0px;
			}
			.labelText{
				font-weight:bold;
			}
			.nowraptext{
				width: 1%;
				white-space: nowrap;
			}
			.cleTable .span12,.memberWrap.span12{
				margin-left:0px!important;
			}	
			.memberWrap.span12{
				line-height: 28px;
			}	
			.filterWrap	 td,.cleHistoryWrap{
				padding-left:0!important;
			}
			.pageDesc{
				padding: 10px 5px;
			}
			.descWrap{
				padding:10px 2px;
			}
			.authWrap{
				font-weight:600;
			}
			.storeWrap{
				padding-top:0px !important;
				padding-bottom:0px !important;
			}
			.storeWrap .authWrap{
				padding-top:5px !important;
			}
			@media print {
				.cleHistoryWrap .cle-d-print-none { display: none !important; }
			}
		</style>
		<script>
			var #ToScript(local.cleData.cleData.pageName,"strPagename")#
			function viewEVCert(rid) {
				var certURL = '/?pg='+strPagename+'&panel=viewEVCert&mode=stream&rid=' + rid;
				window.open(certURL,'ViewCertificate','width=990,height=500');
			}
			function viewStoreCert(aid) {
				var certURL = '/?pg='+strPagename+'&panel=viewStoreCert&mode=stream&aid=' + aid;
				window.open(certURL,'ViewCertificate','width=990,height=500');
			}
			function hideAlert() { $('##issuemsg').html('').hide(); }
			function showAlert(msg) { $('##issuemsg').html(msg).show(); }
			function _FB_validateForm(){
				var theForm = document.forms["frmCLE"];
				var arrReq = new Array();
				if (typeof $('##membernumber') != "undefined" && !_FB_hasValue(theForm['membernumber'], 'TEXT')) arrReq[arrReq.length] ='Must enter MemberNumber before you can filter report.';
				if (arrReq.length > 0) {
					var msg = 'The following fields are required:\n\n';
					for (var i=0; i < arrReq.length; i++) msg += arrReq[i] + '\n';
					showAlert(msg);
					return false;
				}
			}
			function _FB_hasValue(obj, obj_type){
				if (obj_type == 'TEXT' || obj_type == 'TEXTAREA'){ tmp = obj.value; tmp = tmp.replace(/^\s+/,'').replace(/\s+$/,''); if (tmp.length == 0) return false; else return true; }
				else if (obj_type == 'SELECT'){ for (var i=0; i < obj.length; i++) { if (obj.options[i].selected){ tmp = obj.options[i].value; tmp = tmp.replace(/^\s+/,'').replace(/\s+$/,''); if (tmp.length > 0) return true; } } return false; } 
				else if (obj_type == 'SINGLE_VALUE_RADIO' || obj_type == 'SINGLE_VALUE_CHECKBOX'){ if (obj.checked) return true; else return false;	} 
				else if (obj_type == 'RADIO' || obj_type == 'CHECKBOX'){ if (obj.length == undefined && obj.checked) return true; else{for (var i=0; i < obj.length; i++){ if (obj[i].checked) return true; }} return false; }
				else{ return true; }
			}
			function viewCert(eId) {
				var certURL = '/?pg=semWebCatalog&panel=viewCert&mode=direct&eId=' + eId;
				window.open(certURL,'ViewCertificate','width=990,height=500');
			}
			$(document).ready(function(){
				mca_setupDatePickerRangeFields('periodStartDate','periodEndDate');
			});
				
		</script>
	</cfoutput>
</cfsavecontent>
<cfhtmlhead text="#local.myCEFrmJS#">

<cfoutput>	
	<cfform class="form-horizontal" method="POST" action="#local.cleData.BASEURL#" name="frmCLE" id="frmCLE" onsubmit="return _FB_validateForm();" style="margin:0px">					
		<div class="cleRow">
			<div class="span8">
				<span class="TitleText clePageTitle">#local.cleData.cleData.pageTitle#</span>			
			</div>
			<div class="span4">
				<span style="float:right;" class="hiddenPrint">
					<button class="btn cleButton" type="button" onClick="window.print();"><i class="icon-print "></i> Print</button>
				</span>			
			</div>
		</div>
		<div class="cleRow">
			<div class="span12 pageDesc">
				<span >#local.cleData.cleData.pageDesc#</span>			
			</div>
			
		</div>			
		<img class="divider" src="/assets/common/images/dottedLine.png">			
		<div id="issuemsg" class="alert alert-danger hide hiddenPrint"></div>
		<div class="cleRow">
			<cfif local.cleData.allowMemberNumberLookup>
				<div class="span12">
					<b>MemberNumber:</b> &nbsp;
					<cfinput class="form-control" type="text" name="membernumber" id="membernumber" value="#local.cleData.memberdata.membernumber#" size="30" placeholder="Must enter MemberNumber.">
					<button type="submit" class="btn hiddenPrint cleButton" name="btnSubmitGo" value="go">Go</button>
				</div>					
			</cfif>
		</div>
		
		<div class="cleRow">
			<div class="span12 memberWrap">		
				<cfset local.fullName = ''>
				<cfset local.showMemberData = 0>
				<cfset local.companyName = ''>
				<cfset local.strMemberData = ''>
				<cfif local.cleData.fieldSetUID neq ''> 
					<cfset local.fieldSetData = local.cleData.fieldSetData>
					<cfif local.fieldSetData.recordCount AND structKeyExists(local.fieldSetData,'Extended Name')>
						<cfset local.fullName = local.fieldSetData['Extended Name']>
						#replace(replace(local.fullName,'  ',' ','ALL'),' ,',',','ALL')#<br>
						<cfif StructKeyExists(local.cleData.memberData,"COMPANY")>
							<cfset local.companyName = local.cleData.memberData.COMPANY>
							#local.companyName#<br/>
						</cfif>
											
						<cfset local.mc_combinedAddresses = structNew()>
						<cfset local.tmp = XMLSearch(local.cleData.xmlResultFields,"//field[substring(@fieldCode,1,3)='ma_' or substring(@fieldCode,1,3)='mp_' or substring(@fieldCode,1,4)='mat_' or substring(@fieldCode,1,4)='mpt_']")>
						<cfloop array="#local.tmp#" index="local.thisField">
							<cfset local.thisATID = listGetAt(local.thisField.xmlattributes.fieldcode,2,"_")>
							<cfif listFindNoCase("mat,mpt",listGetAt(local.thisField.xmlattributes.fieldcode,1,"_"))>
								<cfset local.strKey = "t#local.thisATID#">
							<cfelse>
								<cfset local.strKey = local.thisATID>
							</cfif>
							<cfif NOT StructKeyExists(local.mc_combinedAddresses,local.strKey)>
								<cfset local.mc_combinedAddresses[local.strKey] = { addr='', type=local.cleData.strOrgAddressTypes[local.strKey] } >
							</cfif>
						</cfloop>
						<cfloop collection="#local.mc_combinedAddresses#" item="local.thisATID">
							<cfsavecontent variable="local.thisATFull">
								<cfif left(local.thisATID,1) eq "t">
									<cfset local.MAfcPrefix = "mat_#right(local.thisATID,len(local.thisATID)-1)#_">
									<cfset local.MPfcPrefix = "mpt_#right(local.thisATID,len(local.thisATID)-1)#_">
								<cfelse>
									<cfset local.MAfcPrefix = "ma_#local.thisATID#_">
									<cfset local.MPfcPrefix = "mp_#local.thisATID#_">
								</cfif>

								<cfset local.tmp = XMLSearch(local.cleData.xmlResultFields,"//field[@fieldCode='#local.MAfcPrefix#address1']")>
								<cfif arrayLen(local.tmp) is 1 and len(local.fieldSetData[local.tmp[1].xmlAttributes.FieldLabel][local.fieldSetData.currentrow])>#local.fieldSetData[local.tmp[1].xmlAttributes.FieldLabel][local.fieldSetData.currentrow]#<br/> </cfif>
								<cfset local.tmp = XMLSearch(local.cleData.xmlResultFields,"//field[@fieldCode='#local.MAfcPrefix#address2']")>
								<cfif arrayLen(local.tmp) is 1 and len(local.fieldSetData[local.tmp[1].xmlAttributes.FieldLabel][local.fieldSetData.currentrow])>#local.fieldSetData[local.tmp[1].xmlAttributes.FieldLabel][local.fieldSetData.currentrow]#<br/> </cfif>
								<cfset local.tmp = XMLSearch(local.cleData.xmlResultFields,"//field[@fieldCode='#local.MAfcPrefix#address3']")>
								<cfif arrayLen(local.tmp) is 1 and len(local.fieldSetData[local.tmp[1].xmlAttributes.FieldLabel][local.fieldSetData.currentrow])>#local.fieldSetData[local.tmp[1].xmlAttributes.FieldLabel][local.fieldSetData.currentrow]#<br/></cfif>
								<cfset local.tmp = XMLSearch(local.cleData.xmlResultFields,"//field[@fieldCode='#local.MAfcPrefix#city']")>
								<cfif arrayLen(local.tmp) is 1 and len(local.fieldSetData[local.tmp[1].xmlAttributes.FieldLabel][local.fieldSetData.currentrow])>#local.fieldSetData[local.tmp[1].xmlAttributes.FieldLabel][local.fieldSetData.currentrow]# </cfif>
								<cfset local.tmp2 = XMLSearch(local.cleData.xmlResultFields,"//field[@fieldCode='#local.MAfcPrefix#stateprov']")>
								<cfif arrayLen(local.tmp2) is 1 and len(local.fieldSetData[local.tmp2[1].xmlAttributes.FieldLabel][local.fieldSetData.currentrow])>, #local.fieldSetData[local.tmp2[1].xmlAttributes.FieldLabel][local.fieldSetData.currentrow]# </cfif>
								<cfset local.tmp = XMLSearch(local.cleData.xmlResultFields,"//field[@fieldCode='#local.MAfcPrefix#postalcode']")>
								<cfif arrayLen(local.tmp) is 1 and len(local.fieldSetData[local.tmp[1].xmlAttributes.FieldLabel][local.fieldSetData.currentrow])> #local.fieldSetData[local.tmp[1].xmlAttributes.FieldLabel][local.fieldSetData.currentrow]#<br/></cfif>
								<cfset local.tmp = XMLSearch(local.cleData.xmlResultFields,"//field[@fieldCode='#local.MAfcPrefix#county']")>
								<cfif arrayLen(local.tmp) is 1 and len(local.fieldSetData[local.tmp[1].xmlAttributes.FieldLabel][local.fieldSetData.currentrow])>#local.fieldSetData[local.tmp[1].xmlAttributes.FieldLabel][local.fieldSetData.currentrow]# County<br/></cfif>
								<cfset local.tmp = XMLSearch(local.cleData.xmlResultFields,"//field[@fieldCode='#local.MAfcPrefix#country']")>
								<cfif arrayLen(local.tmp) is 1 and len(local.fieldSetData[local.tmp[1].xmlAttributes.FieldLabel][local.fieldSetData.currentrow])> #local.fieldSetData[local.tmp[1].xmlAttributes.FieldLabel][local.fieldSetData.currentrow]#<br/></cfif>
								<cfset local.tmp = XMLSearch(local.cleData.xmlResultFields,"//field[substring(@fieldCode,1,#len(local.MPfcPrefix)#)='#local.MPfcPrefix#']")>
								<cfloop array="#local.tmp#" index="local.thisPT">
									<cfif len(local.fieldSetData[local.thisPT.xmlAttributes.FieldLabel][local.fieldSetData.currentrow])>
										<div>#local.thisPT.xmlAttributes.FieldLabel#: #local.fieldSetData[local.thisPT.xmlAttributes.FieldLabel][local.fieldSetData.currentrow]#</div>
									</cfif>
								</cfloop>
							</cfsavecontent>
							<cfset local.thisATfull = trim(replace(replace(local.thisATFull,'  ',' ','ALL'),' ,',',','ALL'))>
							<cfif left(local.thisATfull,2) eq ", ">
								<cfset local.thisATfull = right(local.thisATfull,len(local.thisATfull)-2)>
							</cfif>
							<cfif len(local.thisATfull)>
								<cfset local.mc_combinedAddresses[local.thisATID]['addr'] = local.thisATfull>
							<cfelse>
								<cfset structDelete(local.mc_combinedAddresses,local.thisATID,false)>
							</cfif>
						</cfloop>

					<cfelse>
						<cfset local.showMemberData = 1>
					</cfif>
				<cfelse>					
					<cfset local.showMemberData = 1>
				</cfif>	

				<cfif local.showMemberData eq 1>
					<cfif StructKeyExists(local.cleData.memberData,"FIRSTNAME")>
						<cfset local.fullName = local.fullName & local.cleData.memberData.FIRSTNAME & ' '>
					</cfif>
					<cfif StructKeyExists(local.cleData.memberData,"MIDDLENAME")>
						<cfset local.fullName = local.fullName & local.cleData.memberData.MIDDLENAME & ' '>
					</cfif>
					<cfif StructKeyExists(local.cleData.memberData,"LASTNAME")>
						<cfset local.fullName = local.fullName & local.cleData.memberData.LASTNAME>
					</cfif>
					<cfif StructKeyExists(local.cleData.memberData,"COMPANY")>
						<cfset local.companyName = local.cleData.memberData.COMPANY>
					</cfif>
					#local.fullName# <br/>
					#local.companyName# <br/>
				</cfif>

				<cfif local.showMemberData eq 0>
					<cfif StructCount(local.mc_combinedAddresses)>
						<cfloop collection="#local.mc_combinedAddresses#" item="local.thisATID">
							<div><b style="font-size:95%; margin-right:15px;">#local.mc_combinedAddresses[local.thisATID]['type']#</b><br/>#local.mc_combinedAddresses[local.thisATID]['addr']#</div>
						</cfloop>
					</cfif>
					
					<cfif local.cleData.qryOutputFieldsForLoop.recordCount>
						<cfloop query="local.cleData.qryOutputFieldsForLoop">
							<cfset local.currValue = local.fieldSetData[local.cleData.qryOutputFieldsForLoop.fieldLabel][local.fieldSetData.currentrow]>
							<cfif len(local.currValue)>
								<div>
									#htmlEditFormat(local.cleData.qryOutputFieldsForLoop.fieldLabel)#: 
									<cfif left(local.cleData.qryOutputFieldsForLoop.fieldCode,13) eq 'acct_balance_'>
										#dollarFormat(local.currValue)#
									<cfelse>
										<cfswitch expression="#local.cleData.qryOutputFieldsForLoop.dataTypeCode#">
											<cfcase value="DATE">
												#dateFormat(local.currValue,"m/d/yyyy")#
											</cfcase>
											<cfcase value="STRING,DECIMAL2,INTEGER">
												#local.currValue#
											</cfcase>
											<cfcase value="BIT">
												#YesNoFormat(local.currValue)#
											</cfcase>
										</cfswitch>
									</cfif>
								</div>
							</cfif>
						</cfloop>
					</cfif>
				</cfif>
				<br>
			</div>					
		</div>	
		<cfif local.cleData.qryAuthorities.recordcount gt 0 OR local.cleData.qrySWAuthorities.recordcount gt 0>	
			<div class="BodyTextTitle cleSubHeader" style="margin-bottom:5px;"> <b>Limit to one or more authorities for:</b></br> </div>
		</cfif>	
		<div class="cleRow">
			<div class="span12">
				<cfif local.cleData.qryAuthorities.recordcount gt 0>
				<b><cfif local.cleData.cledata.canShowEvents >#local.cleData.cledata.eventSectionTitle#</cfif><cfif local.cleData.cledata.canShowEvents AND local.cleData.cledata.canShowStore > AND </cfif><cfif local.cleData.cledata.canShowStore > #local.cleData.cledata.StoreSectionTitle#</cfif>:</b> &nbsp;
				</cfif>
				<select class="form-control" name="mcAID" id="mcAID" multiple>
					<option value="ALL">ALL</option>
					<cfloop query="local.cleData.qryAuthorities">
						<option value="#local.cleData.qryAuthorities.authorityID#" <cfif local.cleData.isFilter eq '' OR FindNoCase(local.cleData.qryAuthorities.authorityID,local.cleData.mcAID)>selected</cfif>>#local.cleData.qryAuthorities.authorityName#</option>
					</cfloop>
				</select>
			</div>
		</div>

		<div class="cleRow">
			<div class="span12">
				<cfif local.cleData.qrySWAuthorities.recordcount gt 0>
					<b><cfif local.cleData.cledata.canShowSWL >#local.cleData.cledata.SWLSectionTitle#</cfif><cfif local.cleData.cledata.canShowSWL AND local.cleData.cledata.canShowSWOD > AND </cfif><cfif local.cleData.cledata.canShowSWOD > #local.cleData.cledata.SWODSectionTitle#</cfif>:</b> &nbsp;
				</cfif>
				<select class="form-control" name="swAID" id="swAID" multiple>
					<option value="ALL">ALL</option>
					<cfloop query="local.cleData.qrySWAuthorities">
						<option value="#local.cleData.qrySWAuthorities.CSALinkID#" <cfif local.cleData.isFilter eq '' OR FindNoCase(local.cleData.qrySWAuthorities.CSALinkID,local.cleData.mcAID)>selected</cfif>>#local.cleData.qrySWAuthorities.authorityName#</option>
					</cfloop>
				</select>
			</div>
		</div>

		<div class="cleRow">
			<div class="span12">	
				<br/>	
				<table class="cleTable filterWrap" cellpadding="4" cellspacing="0" width="100%">
					<tr>
						<td class="BodyTextTitle cleSubHeader">
							<b>Date Filter:</b><br>
						</td>
					</tr>
					<tr valign="top">
						<td rowspan="2" class="DatePosition">
							<b class="DateFieldPosition">From</b>
							<cfinput type="text" class="form-control periodDate input-medium" name="periodStartDate" id="periodStartDate" value="#local.cleData.periodStartDate#" autocomplete="off"  size="14"> 
							<b class="DateFieldPosition" style="margin-top: 10px;">to</b>
							<cfinput type="text" class="form-control periodDate input-medium" name="periodEndDate" id="periodEndDate" value="#local.cleData.periodEndDate#" size="14">
						</td>
						<td colspan="6" class="searchPosition">
							<button type="submit" class="btn hiddenPrint cleButton" name="btnSubmit" value="search">Filter Report</button>
						</td>
					</tr>
				</table>
			</div>					
		</div>

		<div class="container-fluid cleHistoryWrap">
			<cfif structKeyExists(local.cleData, "strEvents") and local.cleData.strEvents.qryCE.recordcount>
				<p><img class="divider" style="width:100%;height:1px;" src="/assets/common/images/dottedLine.png"></p>
				<div class=" cleRow HeaderText">
					<b>#local.cleData.cledata.eventSectionTitle#</b>
					<div class=" BodyText descWrap" >				
						#local.cleData.cledata.eventSectionDesc#				
					</div>
				</div>
				<p class="cleSubHeader BodyTextTitle">Summary</p>					
				<table class="cleTable table">
					<tr  class="mobileHide">
						<th class="labelText">Year</th>
						<th class="labelText" style="text-align:left;">Credit&nbsp;Awarded</th>
					</tr>	
					<cfset local.yearsplit = ''>	
					<cfoutput query="local.cleData.strEvents.qryCETotals" group="CLEYear">
						<tr>
							<td data-title="Year: ">
								#local.cleData.strEvents.qryCETotals.CLEYear#
							</td>
							<td data-title="Credit Awarded: ">
								<cfset local.authorityName = ''>
								<cfoutput>
									<cfif local.authorityName neq local.cleData.strEvents.qryCETotals.authorityName>											
										<div class="span12 authWrap">#local.cleData.strEvents.qryCETotals.authorityName#</div>
										<cfset local.authorityName = local.cleData.strEvents.qryCETotals.authorityName>
									</cfif> 
									<cfif local.yearsplit neq local.cleData.strEvents.qryCETotals.CLEYear>										
										<cfset local.yearsplit = local.cleData.strEvents.qryCETotals.CLEYear>
									</cfif>	
									<div class="span12">
										#NumberFormat(local.cleData.strEvents.qryCETotals.totalCLE,'0.00')# #local.cleData.strEvents.qryCETotals.typeName#
									</div>								
								</cfoutput>
							</td>
						</tr>
					</cfoutput>
				</table>
				<br>
				<p class="cleSubHeader BodyTextTitle">DETAIL</p>
				<table class="cleTable table">
					<tr  class="mobileHide">
						<th class="labelText">Date</th>
						<th class="labelText">Title</th>
						<th class="labelText">Credit&nbsp;Awarded</th>
						<th class="labelText">Certificate</th>
					</tr>
					<cfset local.eventid = ''>
					<cfoutput query="local.cleData.strEvents.qryCE" GROUP="eventid">
						<tr>
							<td data-title="Date: " class="nowraptext">#dateformat(local.cleData.strEvents.qryCE.eventStart,"mm/dd/yyyy")#</td>
							<td data-title="Title: ">#local.cleData.strEvents.qryCE.eventTitle#</td>
							<td data-title="Credit Awarded: " class="nowraptext">
								<cfset local.authorityName = ''>
								<cfoutput>
									<cfif local.authorityName neq local.cleData.strEvents.qryCE.authorityName>
										<div class="span12 authWrap">
											#local.cleData.strEvents.qryCE.authorityName#
										</div> 
										<cfset local.authorityName = local.cleData.strEvents.qryCE.authorityName>
									</cfif>
									<cfif local.eventid neq local.cleData.strEvents.qryCE.eventid>
										<cfset local.eventid = local.cleData.strEvents.qryCE.eventid>
									</cfif>
									<div class="span12">#NumberFormat(local.cleData.strEvents.qryCE.creditValueAwarded,'0.00')# #local.cleData.strEvents.qryCE.typeName#</div>
								</cfoutput>
							</td>
							<td data-title="Certificate: " class="nowraptext">
								<cfif local.cleData.strEvents.qryCE.showCert>
									<cfset local.rID = Replace(URLEncodedFormat(ToBase64(Encrypt(local.cleData.strEvents.qryCE.registrantID,"TRiaL_SMiTH"))),"%","xPcmKx","ALL")>
									<button class="btn" type="button" onClick="viewEVCert('#local.rID#');" title="Print Certificate"><i class="icon-certificate"></i> View Certificate</button>
								</cfif>
							</td>
						</tr>
					</cfoutput>
				</table>
				<br/><br/>	
			</cfif>

			<cfif structKeyExists(local.cleData, "strSWL") and arraylen(local.cleData.strSWL.arrSeminars)>
				<p><img class="divider" style="width:100%;height:1px;" src="/assets/common/images/dottedLine.png"></p>
				<div class=" cleRow HeaderText">
					<b>#local.cleData.cledata.SWLSectionTitle#</b>
					<div class=" BodyText descWrap">				
						#local.cleData.cledata.SWLSectionDesc#				
					</div>
				</div>
				<p class="cleSubHeader BodyTextTitle">Summary</p> 
				<table class="cleTable table">
					<tr  class="mobileHide">
						<th class="labelText">Year</th>
						<th class="labelText">Credit&nbsp;Awarded</th>
					</tr>	
					<cfset local.yearsplit = ''>	
					<cfoutput query="local.cleData.strSWL.qryCETotals" group="CLEYear">
						<tr>
							<td data-title="Year: ">
								#local.cleData.strSWL.qryCETotals.CLEYear#
							</td>
							<td data-title="Credit Awarded: ">
								<cfset local.authorityName = ''>
								<cfoutput>
									<cfif local.authorityName neq local.cleData.strSWL.qryCETotals.authorityName>											
										<div class="span12 authWrap">#local.cleData.strSWL.qryCETotals.authorityName#</div>
										<cfset local.authorityName = local.cleData.strSWL.qryCETotals.authorityName>
									</cfif> 
									<cfif local.yearsplit neq local.cleData.strSWL.qryCETotals.CLEYear>
										<cfset local.yearsplit = local.cleData.strSWL.qryCETotals.CLEYear>
									</cfif>	
									<div class="span12">											
									#NumberFormat(local.cleData.strSWL.qryCETotals.totalCLE,'0.00')# #local.cleData.strSWL.qryCETotals.creditType#</div>								
								</cfoutput>
							</td>
						</tr>
					</cfoutput>	
				</table>
				<br>
				<p class="cleSubHeader BodyTextTitle">DETAIL</p>
				<table class="cleTable table">
				<tr class="mobileHide">
					<th class="labelText">Date</th>
					<th class="labelText">Title</th>
					<th class="labelText">Credit&nbsp;Awarded</th>
					<th class="labelText">Certificate</th>
				</tr>
				<cfloop array="#local.cleData.strSWL.arrSeminars#" index="local.thisSeminar">
					<cfquery name="local.thisSeminarCredits" dbtype="query">
						SELECT authorityName, creditValueAwarded, creditType
						FROM [local].cleData.strSWL.qryAllCredits
						WHERE enrollmentID = #local.thisSeminar.enrollmentID#
						ORDER BY authorityName, creditType
					</cfquery>
					<tr>
						<td data-title="Date: " class="nowraptext">#DateFormat(local.thisSeminar.date,'m/d/yyyy')#</td>
						<td data-title="Title: ">
							#local.thisSeminar.title#
							<cfif len(local.thisSeminar.replayVideoLink)>
								<br /><a href="#local.thisSeminar.replayVideoLink#" class="tsAppBodyText cle-d-print-none" target="_blank"><i class="icon-play" aria-hidden="true" title="Replay Video"></i> Replay Video</a>
							</cfif>
						</td>
						<td data-title="Credit Awarded: " class="nowraptext">
							<cfoutput query="local.thisSeminarCredits" group="authorityName">
								<div class="span12 authWrap">											
									#local.thisSeminarCredits.authorityName#											
								</div> 
								<cfoutput>
									<div class="span12">
										#NumberFormat(local.thisSeminarCredits.creditValueAwarded,'0.00')# #local.thisSeminarCredits.creditType#
									</div>
								</cfoutput>
							</cfoutput>
						</td>
						<td data-title="Certificate: " class="nowraptext">
							<cfif local.thisSeminar.canViewCertificate>
								<button class="btn" type="button" onClick="viewCert('#local.thisSeminar.encryptedEID#');return false;" title="Print Certificate"><i class="icon-certificate"></i> View Certificate</button>
							</cfif>
						</td>
					</tr>
				</cfloop>
				</table>
				<br/><br/>
			</cfif>

			<cfif structKeyExists(local.cleData, "strSWOD") and arraylen(local.cleData.strSWOD.arrSeminars)>
				<p><img class="divider" style="width:100%;height:1px;" src="/assets/common/images/dottedLine.png"></p>
				<div class=" cleRow HeaderText">
					<b>#local.cleData.cledata.SWODSectionTitle#</b>
					<div class=" BodyText descWrap">				
						#local.cleData.cledata.SWODSectionDesc#				
					</div>
				</div>
				<p class="cleSubHeader BodyTextTitle">Summary</p> 
				<table class="cleTable table">		
					<tr  class="mobileHide">
						<th class="labelText">Year</th>
						<th class="labelText">Credit&nbsp;Awarded</th>
					</tr>	
					<cfset local.yearsplit = ''>	
					<cfoutput query="local.cleData.strSWOD.qryCETotals" group="CLEYear">
						<tr>
							<td data-title="Year: ">
								#local.cleData.strSWOD.qryCETotals.CLEYear#
							</td>
							<td data-title="Credit Awarded: ">
								<cfset local.authorityName = ''>
								<cfoutput>
									<cfif local.authorityName neq local.cleData.strSWOD.qryCETotals.authorityName>											
										<div class="span12 authWrap">#local.cleData.strSWOD.qryCETotals.authorityName#</div>
										<cfset local.authorityName = local.cleData.strSWOD.qryCETotals.authorityName>
									</cfif> 
									<cfif local.yearsplit neq local.cleData.strSWOD.qryCETotals.CLEYear>
										<cfset local.yearsplit = local.cleData.strSWOD.qryCETotals.CLEYear>
									</cfif>	
									<div class="span12">											
									#NumberFormat(local.cleData.strSWOD.qryCETotals.totalCLE,'0.00')# #local.cleData.strSWOD.qryCETotals.creditType#</div>								
								</cfoutput>
							</td>
						</tr>
					</cfoutput>	
				</table>
				<br>
				<p class="cleSubHeader BodyTextTitle">DETAIL</p>
				<table class="cleTable table">	
				<tr class="mobileHide">
					<th class="labelText">Date</th>
					<th class="labelText">Title</th>
					<th class="labelText">Credit&nbsp;Awarded</th>
					<th class="labelText">Certificate</th>
				</tr>
				<cfloop array="#local.cleData.strSWOD.arrSeminars#" index="local.thisSeminar">
					<cfquery name="local.thisSeminarCredits" dbtype="query">
						SELECT authorityName, creditValueAwarded, creditType
						FROM [local].cleData.strSWOD.qryAllCredits
						WHERE enrollmentID = #local.thisSeminar.enrollmentID#
						ORDER BY authorityName, creditType
					</cfquery>
					<tr>
						<td data-title="Enrolled&nbsp;Date: " class="nowraptext">#DateFormat(local.thisSeminar.dateEnrolled,'m/d/yyyy')#</td>
						<td data-title="Title: ">#local.thisSeminar.title#</td>
						<td data-title="Credit Awarded: " class="nowraptext">
							<cfoutput query="local.thisSeminarCredits" group="authorityName">
								<div class="span12 authWrap">											
									#local.thisSeminarCredits.authorityName#											
								</div> 
								<cfoutput>
									<div class="span12">
										#NumberFormat(local.thisSeminarCredits.creditValueAwarded,'0.00')# #local.thisSeminarCredits.creditType#
									</div>
								</cfoutput>
							</cfoutput>
						</td>
						<td data-title="Certificate: " class="nowraptext">
							<cfif local.thisSeminar.canViewCertificate>
								<button class="btn" type="button" onClick="viewCert('#local.thisSeminar.encryptedEID#');return false;" title="Print Certificate"><i class="icon-certificate"></i> View Certificate</button>
							</cfif>
						</td>
					</tr>
				</cfloop>
				</table>
				<br/><br/>
			</cfif> 
			
			<cfif structKeyExists(local.cleData, "strStore") and local.cleData.strStore.qryCE.recordcount>	
				<p><img class="divider" style="width:100%;height:1px;" src="/assets/common/images/dottedLine.png"></p>
				<div class=" cleRow HeaderText">
					<b>#local.cleData.cledata.storeSectionTitle#</b>
					<div class=" BodyText descWrap">
						#local.cleData.cledata.storeSectionDesc#
					</div>
				</div>
				<p class="cleSubHeader BodyTextTitle">Summary</p>
							
				<table class="cleTable table">
					<tr class="mobileHide">
						<th class="labelText">Year</th>
						<th class="labelText">Credit&nbsp;Awarded</th>
					</tr>	
					<cfset local.yearsplit = ''>
					<cfoutput query="local.cleData.strStore.qryCETotals" group="CLEYear">
						<tr>
							<td data-title="Year: ">
								#local.cleData.strStore.qryCETotals.CLEYear#
							</td>
							<td data-title="Credit Awarded: " class="storeWrap">
								<cfset local.authorityName = ''>
								<cfoutput>
									<cfif local.authorityName neq local.cleData.strStore.qryCETotals.authorityName>											
										<div class="span12 authWrap">#local.cleData.strStore.qryCETotals.authorityName#</div>
										<cfset local.authorityName = local.cleData.strStore.qryCETotals.authorityName>
									</cfif> 
									<cfif local.yearsplit neq local.cleData.strStore.qryCETotals.CLEYear>
											<cfset local.yearsplit = local.cleData.strStore.qryCETotals.CLEYear>
										</cfif>	
									<div class="span12">												
										#NumberFormat(local.cleData.strStore.qryCETotals.totalCLE,'0.00')# #local.cleData.strStore.qryCETotals.typeName#</div>								
								</cfoutput>
							</td>
						</tr>
					</cfoutput>	
				</table>
				<br>
				
				<p class="cleSubHeader BodyTextTitle">Detail</p>
				
				<table class="cleTable table">	
					<tr  class="mobileHide">
						<th class="labelText">Date</th>
						<th class="labelText">Title</th>
						<th class="labelText">Credit&nbsp;Awarded</th>
						<th class="labelText">Certificate</th>
					</tr>
					<cfset local.affirmationid = ''>	
					<cfoutput query="local.cleData.strStore.qryCE" GROUP="affirmationid">
						<tr>
							<td data-title="Date: " class="nowraptext">#dateformat(local.cleData.strStore.qryCE.dateClaimed,"mm/d/yyyy")#</td>
							<td data-title="Title: ">#local.cleData.strStore.qryCE.productTitle#</td>
							<td data-title="Credit Awarded: " class="nowraptext ">
								<cfset local.authorityName = ''>
								<cfoutput>
									<cfif local.authorityName neq local.cleData.strStore.qryCE.authorityName>
										<div class="span12 authWrap">											
											#local.cleData.strStore.qryCE.authorityName#											
										</div> 
										<cfset local.authorityName = local.cleData.strStore.qryCE.authorityName>
									</cfif>
									<cfif local.affirmationid neq local.cleData.strStore.qryCE.affirmationid>
										<cfset local.affirmationid = local.cleData.strStore.qryCE.affirmationid>
									</cfif>	
									<div class="span12">#NumberFormat(local.cleData.strStore.qryCE.creditValueAwarded,'0.00')# #local.cleData.strStore.qryCE.typeName#</div>
								</cfoutput>
							</td>
							<td data-title="Certificate: " class="nowraptext">
								<cfset local.aID = Replace(URLEncodedFormat(ToBase64(Encrypt(local.cleData.strStore.qryCE.affirmationID,"TRiaL_SMiTH"))),"%","xPcmKx","ALL")>
								<button class="btn" type="button" onClick="viewStoreCert('#local.aID#');" title="Print Certificate"><i class="icon-certificate"></i> View Certificate</button>
							</td>
						</tr>
					</cfoutput>
				</table>
				<br/><br/>
			</cfif>
		</div>		
	</cfform>
</cfoutput>